import { IKeycloakTokenResponse, IKeycloakUserInfo, IKeycloakCallbackParams } from "../interfaces/keycloak-config.interface";
import { KeycloakConfiguration } from "../config/keycloak.config";
import { ApiResponse } from "@/shared/types/requests/request.type";

/**
 * Interface para o serviço de autenticação Keycloak
 * Segue a convenção I<Ação><Nome><Escopo>
 */
export interface IKeycloakAuthenticationService {
	exchangeCodeForToken(code: string, state: string): Promise<ApiResponse<IKeycloakTokenResponse>>;
	getUserInfo(accessToken: string): Promise<ApiResponse<IKeycloakUserInfo>>;
	validateState(receivedState: string, expectedState: string): boolean;
}

/**
 * Serviço de autenticação Keycloak seguindo princípios SOLID
 * Implementa Dependency Inversion Principle (DIP) e Single Responsibility Principle (SRP)
 */
export class KeycloakAuthenticationService implements IKeycloakAuthenticationService {
	private readonly config: KeycloakConfiguration;

	constructor(config?: KeycloakConfiguration) {
		this.config = config || KeycloakConfiguration.getInstance();
	}

	/**
	 * Troca o código de autorização por um token de acesso
	 */
	public async exchangeCodeForToken(code: string, state: string): Promise<ApiResponse<IKeycloakTokenResponse>> {
		try {
			const urls = this.config.getAuthenticationUrls();

			const body = new URLSearchParams({
				grant_type: "authorization_code",
				client_id: this.config.clientId,
				code,
				redirect_uri: this.config.redirectUri,
			});

			const response = await fetch(urls.tokenUrl, {
				method: "POST",
				headers: {
					"Content-Type": "application/x-www-form-urlencoded",
					Accept: "application/json",
				},
				body: body.toString(),
			});

			if (!response.ok) {
				const errorText = await response.text();
				return {
					success: false,
					data: { message: `Erro ao trocar código por token: ${errorText}` },
					status: response.status,
				};
			}

			const tokenData: IKeycloakTokenResponse = await response.json();

			return {
				success: true,
				data: tokenData,
				status: 200,
			};
		} catch (error) {
			return {
				success: false,
				data: {
					message: `Erro na comunicação com Keycloak: ${error instanceof Error ? error.message : "Erro desconhecido"}`,
				},
				status: 500,
			};
		}
	}

	/**
	 * Obtém informações do usuário usando o token de acesso
	 */
	public async getUserInfo(accessToken: string): Promise<ApiResponse<IKeycloakUserInfo>> {
		try {
			const urls = this.config.getAuthenticationUrls();

			const response = await fetch(urls.userInfoUrl, {
				method: "GET",
				headers: {
					Authorization: `Bearer ${accessToken}`,
					Accept: "application/json",
				},
			});

			if (!response.ok) {
				const errorText = await response.text();
				return {
					success: false,
					data: { message: `Erro ao obter informações do usuário: ${errorText}` },
					status: response.status,
				};
			}

			const userInfo: IKeycloakUserInfo = await response.json();

			return {
				success: true,
				data: userInfo,
				status: 200,
			};
		} catch (error) {
			return {
				success: false,
				data: {
					message: `Erro ao obter informações do usuário: ${error instanceof Error ? error.message : "Erro desconhecido"}`,
				},
				status: 500,
			};
		}
	}

	/**
	 * Valida o parâmetro state para prevenir ataques CSRF
	 */
	public validateState(receivedState: string, expectedState: string): boolean {
		if (!receivedState || !expectedState) {
			return false;
		}

		return receivedState === expectedState;
	}

	/**
	 * Gera um estado único para prevenção de CSRF
	 */
	public generateState(): string {
		return crypto.randomUUID();
	}

	/**
	 * Gera URL de autorização com estado
	 */
	public generateAuthorizationUrl(redirectPath?: string): { url: string; state: string } {
		const state = this.generateState();
		const scope = "openid profile email";

		// Se há um path de redirecionamento, incluir no state (base64 encoded)
		const stateWithRedirect = redirectPath ? `${state}:${btoa(redirectPath)}` : state;

		const url = this.config.buildAuthorizationUrl(stateWithRedirect, scope);

		return { url, state: stateWithRedirect };
	}

	/**
	 * Extrai o path de redirecionamento do state
	 */
	public extractRedirectFromState(state: string): string | null {
		try {
			const parts = state.split(":");
			if (parts.length === 2) {
				return atob(parts[1]);
			}
			return null;
		} catch {
			return null;
		}
	}
}
