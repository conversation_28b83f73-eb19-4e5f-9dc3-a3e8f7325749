/**
 * Interface para configuração do Keycloak
 * Segue a convenção I<Ação><Nome><Escopo>
 */
export interface IKeycloakConfigurationProvider {
	readonly baseUrl: string;
	readonly realm: string;
	readonly clientId: string;
	readonly redirectUri: string;
}

/**
 * Interface para URLs de autenticação do Keycloak
 */
export interface IKeycloakAuthenticationUrls {
	readonly authorizationUrl: string;
	readonly tokenUrl: string;
	readonly logoutUrl: string;
	readonly userInfoUrl: string;
}

/**
 * Interface para parâmetros de autorização
 */
export interface IKeycloakAuthorizationParams {
	readonly clientId: string;
	readonly redirectUri: string;
	readonly responseType: string;
	readonly scope: string;
	readonly state: string;
}

/**
 * Interface para resposta de token do Keycloak
 */
export interface IKeycloakTokenResponse {
	readonly access_token: string;
	readonly token_type: string;
	readonly expires_in: number;
	readonly refresh_token?: string;
	readonly scope: string;
}

/**
 * Interface para dados do usuário do Keycloak
 */
export interface IKeycloakUserInfo {
	readonly sub: string;
	readonly name: string;
	readonly preferred_username: string;
	readonly email?: string;
	readonly email_verified?: boolean;
	readonly roles?: string[];
}

/**
 * Interface para parâmetros de callback
 */
export interface IKeycloakCallbackParams {
	readonly code: string;
	readonly state: string;
	readonly sessionState?: string;
}

/**
 * Interface para erro de callback
 */
export interface IKeycloakCallbackError {
	readonly error: string;
	readonly error_description?: string;
	readonly state?: string;
}
