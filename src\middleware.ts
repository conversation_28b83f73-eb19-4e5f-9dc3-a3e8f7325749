import { NextRequest, NextResponse } from "next/server";
import { authMiddleware } from "./config/middleware/auth/auth.middleware";
import { permissionsMiddleware } from "./config/middleware/auth/route.middleware";
import { SecurityHeadersMiddleware } from "./config/middleware/security/security-headers.middleware";

export async function middleware(request: NextRequest): Promise<Response> {
	const { pathname } = request.nextUrl;

	console.log("🔍 Middleware executando para:", pathname);

	// Permite acesso às rotas de autenticação Keycloak sem validação
	if (pathname.startsWith("/auth/")) {
		console.log("✅ Rota de auth permitida:", pathname);
		return SecurityHeadersMiddleware.addToResponse(NextResponse.next());
	}

	// Executa middleware de autenticação
	const authResponse = await authMiddleware(request);
	console.log("🔐 Auth Response Status:", authResponse.status);
	console.log("🔐 Auth Response Headers:", Object.fromEntries(authResponse.headers.entries()));

	// Verifica se há redirecionamento (status 302) ou rewrite
	if (
		authResponse.status === 302 ||
		authResponse.headers.get("x-middleware-rewrite") ||
		authResponse.headers.get("x-middleware-redirect") ||
		authResponse.headers.get("location")
	) {
		console.log("🔄 Redirecionamento detectado, retornando auth response");
		return SecurityHeadersMiddleware.addToResponse(authResponse);
	}

	// Executa middleware de permissões
	const permissionsResponse = permissionsMiddleware(request);
	console.log("🔑 Permissions Response Status:", permissionsResponse.status);

	if (
		permissionsResponse.status === 302 ||
		permissionsResponse.headers.get("x-middleware-rewrite") ||
		permissionsResponse.headers.get("x-middleware-redirect") ||
		permissionsResponse.headers.get("location")
	) {
		console.log("🔄 Redirecionamento de permissões detectado");
		return SecurityHeadersMiddleware.addToResponse(permissionsResponse);
	}

	console.log("✅ Middleware concluído sem redirecionamentos");
	return SecurityHeadersMiddleware.addToResponse(NextResponse.next());
}

export const config = {
	matcher: ["/((?!api|_next/static|_next/image|_next/font|favicon.ico|events/devtools/events|events$|\\.well-known).*)"],
};
