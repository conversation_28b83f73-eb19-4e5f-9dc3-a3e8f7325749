"use client";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useSetAtom } from "jotai";
import { userAtom } from "../../atoms/user.atom";
import { isAuthenticatedAtom } from "../../atoms/auth.atom";
import { KeycloakIntegrationService } from "@/config/keycloack";
import { toast } from "@/core/toast";

/**
 * Interface para retorno do hook de logout Keycloak
 * Segue a convenção I<Ação><Nome><Escopo>
 */
export interface IUseKeycloakLogoutHook {
	logout: () => void;
	isLoggingOut: boolean;
}

/**
 * Hook para logout integrado com Keycloak
 * Mantém compatibilidade com sistema existente de gerenciamento de estado
 */
export function useKeycloakLogout(): IUseKeycloakLogoutHook {
	const setUser = useSetAtom(userAtom);
	const setIsAuthenticated = useSetAtom(isAuthenticatedAtom);
	const queryClient = useQueryClient();
	const keycloakIntegration = new KeycloakIntegrationService();

	const mutation = useMutation({
		mutationKey: ["keycloak-logout"],
		mutationFn: async () => {
			// Processa logout integrado (limpa tokens locais e gera URL Keycloak)
			const logoutResult = await keycloakIntegration.processLogout(
				process.env.NEXT_PUBLIC_APP_URL
			);

			if (!logoutResult.success) {
				throw new Error("Erro ao processar logout");
			}

			// Limpa estado local
			setUser(null);
			setIsAuthenticated(false);
			queryClient.clear();

			// Redireciona para logout do Keycloak
			window.location.href = logoutResult.data.logoutUrl;

			return true;
		},
		onError: (error) => {
			console.error("Erro no logout Keycloak:", error);
			toast.error("Erro ao fazer logout");
		},
	});

	return {
		logout: () => mutation.mutate(),
		isLoggingOut: mutation.isPending,
	};
}
