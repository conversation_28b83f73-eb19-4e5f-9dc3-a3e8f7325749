import { NextRequest, NextResponse } from "next/server";
import { KeycloakAuthenticationService } from "@/config/keycloack";
import { setAuthTokens } from "@/core/auth/lib/auth-actions";

/**
 * Interface para parâmetros de callback do Keycloak
 */
interface IKeycloakCallbackQuery {
	code?: string;
	state?: string;
	error?: string;
	error_description?: string;
	session_state?: string;
}

/**
 * Interface para resposta de erro de callback
 */
interface ICallbackErrorResponse {
	error: string;
	message: string;
	redirect_to?: string;
}

/**
 * Manipulador GET para /auth/callback
 * Processa o retorno do Keycloak após autenticação
 * 
 * @param request - Requisição Next.js
 * @returns Redirecionamento para aplicação ou página de erro
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
	try {
		const { searchParams } = new URL(request.url);
		
		// Extrai parâmetros do callback
		const callbackParams: IKeycloakCallbackQuery = {
			code: searchParams.get("code") || undefined,
			state: searchParams.get("state") || undefined,
			error: searchParams.get("error") || undefined,
			error_description: searchParams.get("error_description") || undefined,
			session_state: searchParams.get("session_state") || undefined
		};

		// Verifica se houve erro no Keycloak
		if (callbackParams.error) {
			console.error("Erro de autenticação Keycloak:", {
				error: callbackParams.error,
				description: callbackParams.error_description
			});

			return redirectToErrorPage(
				"keycloak_auth_error",
				`Erro de autenticação: ${callbackParams.error_description || callbackParams.error}`
			);
		}

		// Valida presença do código e estado
		if (!callbackParams.code || !callbackParams.state) {
			return redirectToErrorPage(
				"missing_parameters",
				"Parâmetros de callback ausentes"
			);
		}

		// Recupera e valida o estado armazenado
		const storedState = request.cookies.get("keycloak_state")?.value;
		if (!storedState) {
			return redirectToErrorPage(
				"missing_state",
				"Estado de autenticação não encontrado"
			);
		}

		// Inicializa serviço Keycloak
		const keycloakService = new KeycloakAuthenticationService();

		// Valida estado para prevenir CSRF
		if (!keycloakService.validateState(callbackParams.state, storedState)) {
			return redirectToErrorPage(
				"invalid_state",
				"Estado de autenticação inválido"
			);
		}

		// Troca código por token
		const tokenResponse = await keycloakService.exchangeCodeForToken(
			callbackParams.code,
			callbackParams.state
		);

		if (!tokenResponse.success) {
			console.error("Erro ao trocar código por token:", tokenResponse.data);
			return redirectToErrorPage(
				"token_exchange_error",
				"Erro ao obter token de acesso"
			);
		}

		// Armazena token usando o sistema existente
		const setTokenResult = await setAuthTokens(tokenResponse.data.access_token);
		if (!setTokenResult.success) {
			console.error("Erro ao armazenar token:", setTokenResult.data);
			return redirectToErrorPage(
				"token_storage_error",
				"Erro ao armazenar credenciais"
			);
		}

		// Extrai path de redirecionamento do estado
		const redirectPath = keycloakService.extractRedirectFromState(callbackParams.state) || "/";

		// Cria resposta de redirecionamento
		const response = NextResponse.redirect(new URL(redirectPath, request.url), { status: 302 });
		
		// Remove cookie de estado
		response.cookies.delete("keycloak_state");

		return response;

	} catch (error) {
		console.error("Erro no processamento de callback Keycloak:", error);
		
		return redirectToErrorPage(
			"internal_error",
			"Erro interno no processamento de autenticação"
		);
	}
}

/**
 * Redireciona para página de erro com parâmetros
 * 
 * @param error - Código do erro
 * @param message - Mensagem do erro
 * @returns NextResponse com redirecionamento
 */
function redirectToErrorPage(error: string, message: string): NextResponse {
	const errorUrl = new URL("/forbidden", process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000");
	errorUrl.searchParams.set("error", error);
	errorUrl.searchParams.set("message", message);
	
	const response = NextResponse.redirect(errorUrl, { status: 302 });
	
	// Remove cookie de estado em caso de erro
	response.cookies.delete("keycloak_state");
	
	return response;
}
