import { AccessTokenValidator } from "@/config/services/access-token-validator";
import { NextRequest, NextResponse } from "next/server";

/**
 * Middleware de autenticação integrado com Keycloak
 * Mantém compatibilidade com sistema existente de validação de tokens
 * Redireciona para Keycloak quando não autenticado
 */
export async function authMiddleware(request: Readonly<NextRequest>): Promise<NextResponse> {
	const { pathname } = request.nextUrl;

	// Usa o validador existente para verificar tokens
	const accessTokenValidator = new AccessTokenValidator();
	const validationResult = await accessTokenValidator.validate(request);

	// Se token inválido ou ausente, redireciona para autenticação Keycloak
	if (!validationResult.isValid || validationResult.status !== "ok") {
		// Redireciona para o route handler /auth/login que gerencia Keycloak
		const keycloakLoginUrl = new URL("/auth/login", request.url);
		keycloakLoginUrl.searchParams.set("redirect", pathname);

		// Usa redirect em vez de rewrite para permitir que o route handler processe
		return NextResponse.redirect(keycloakLoginUrl, { status: 302 });
	}

	return NextResponse.next();
}
