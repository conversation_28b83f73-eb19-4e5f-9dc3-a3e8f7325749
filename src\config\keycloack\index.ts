// Configuração
export { KeycloakConfiguration } from "./config/keycloak.config";

// Serviços
export { KeycloakAuthenticationService, type IKeycloakAuthenticationService } from "./services/keycloak-auth.service";

export {
	KeycloakIntegrationService,
	type IKeycloakIntegrationService,
	type IKeycloakIntegratedAuthResult,
} from "./services/keycloak-integration.service";

// Interfaces
export type {
	IKeycloakConfigurationProvider,
	IKeycloakAuthenticationUrls,
	IKeycloakAuthorizationParams,
	IKeycloakTokenResponse,
	IKeycloakUserInfo,
	IKeycloakCallbackParams,
	IKeycloakCallbackError,
} from "./interfaces/keycloak-config.interface";
