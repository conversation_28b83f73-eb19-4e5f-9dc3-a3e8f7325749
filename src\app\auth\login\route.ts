import { NextRequest, NextResponse } from "next/server";
import { KeycloakAuthenticationService } from "@/config/keycloack";

/**
 * Interface para parâmetros de query da requisição de login
 */
interface ILoginRequestQuery {
	redirect?: string;
}

/**
 * Interface para resposta de erro
 */
interface ILoginErrorResponse {
	error: string;
	message: string;
}

/**
 * Manipulador GET para /auth/login
 * Redireciona para o Keycloak para autenticação
 *
 * @param request - Requisição Next.js
 * @returns Redirecionamento 302 para Keycloak ou erro
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
	try {
		// Extrai parâmetros de query
		const { searchParams } = new URL(request.url);
		const redirectPath = searchParams.get("redirect") || "/";

		// Valida o path de redirecionamento para prevenir open redirect
		if (!isValidRedirectPath(redirectPath)) {
			return NextResponse.json(
				{
					error: "invalid_redirect",
					message: "Path de redirecionamento inválido",
				} as ILoginErrorResponse,
				{ status: 400 }
			);
		}

		// Inicializa o serviço de autenticação Keycloak
		const keycloakService = new KeycloakAuthenticationService();

		// Gera URL de autorização com estado
		const { url: authorizationUrl, state } = keycloakService.generateAuthorizationUrl(redirectPath);

		// Armazena o estado em cookie para validação posterior
		const response = NextResponse.redirect(authorizationUrl, { status: 302 });

		// Define cookie com estado para validação CSRF
		response.cookies.set("keycloak_state", state, {
			httpOnly: true,
			secure: process.env.NODE_ENV === "production",
			sameSite: "lax",
			maxAge: 600, // 10 minutos
			path: "/",
		});

		return response;
	} catch (error) {
		console.error("Erro no redirecionamento para Keycloak:", error);

		return NextResponse.json(
			{
				error: "keycloak_error",
				message: "Erro interno ao processar redirecionamento para autenticação",
			} as ILoginErrorResponse,
			{ status: 500 }
		);
	}
}

/**
 * Valida se o path de redirecionamento é seguro
 * Previne ataques de open redirect
 *
 * @param path - Path a ser validado
 * @returns true se o path é válido, false caso contrário
 */
function isValidRedirectPath(path: string): boolean {
	// Não permite URLs absolutas ou protocolos
	if (path.includes("://") || path.startsWith("//")) {
		return false;
	}

	// Deve começar com /
	if (!path.startsWith("/")) {
		return false;
	}

	// Não permite caracteres perigosos
	const dangerousChars = ["<", ">", '"', "'", "&"];
	if (dangerousChars.some(char => path.includes(char))) {
		return false;
	}

	// Lista de paths permitidos (pode ser expandida conforme necessário)
	const allowedPaths = ["/", "/variaveis", "/forbidden"];

	// Permite paths que começam com paths permitidos
	return allowedPaths.some(allowedPath => path === allowedPath || path.startsWith(allowedPath + "/"));
}
